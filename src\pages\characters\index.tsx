import React, { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { Character } from "../../types"
import { Input } from "antd"

const Characters: React.FC = () => {
  const [characters, setCharacters] = useState<Character[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [seats, setSeats] = useState([])
  const [isEmpty, setIsEmpty] = useState(true)

  useEffect(() => {
    fetchCharacters()
  }, [])

  const fetchCharacters = async () => {}

  const filteredCharacters = characters.filter(
    character =>
      character.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      character.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="text-center text-white">
        <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-400"></div>
        <p className="mt-4">加载中...</p>
      </div>
    )
  }
  useEffect(() => {
    if (!searchTerm) {
      setSeats([])
      setIsEmpty(true)
      return
    }

    const seatCount = parseInt(searchTerm, 10)
    if (seatCount < 1 || seatCount > 9) {
      setSeats([])
      setIsEmpty(true)
      return
    }

    // 生成席位数据
    const newSeats = []
    for (let i = 0; i < seatCount; i++) {
      newSeats.push({
        id: i,
        type: i === 0 ? "decision" : "normal", // 第一个为决策席位
        name: i === 0 ? "决策席位" : `一般席位 ${i}`,
      })
    }

    setSeats(newSeats)
    setIsEmpty(false)
  }, [searchTerm])
  const renderSeatCard = seat => (
    <div
      key={seat.id}
      className={`relative m-2 h-[197px] w-[480px] rounded-xl p-4 shadow-lg transition-all duration-300 ${seat.type === "decision" ? "bg-gradient-to-br from-blue-600 to-blue-800" : "bg-gradient-to-br from-gray-700 to-gray-900"} hover:scale-105 hover:shadow-xl`}
    >
      <div className="${seat.type === 'decision' ? 'bg-yellow-400 text-gray-900' : 'bg-blue-400 text-gray-900'} absolute left-3 top-3 rounded-full px-2 py-1 text-xs font-bold">
        {seat.type === "decision" ? "决策席" : "一般席"}
      </div>
      <div className="flex h-full items-center justify-center">
        <span className="text-xl font-semibold text-white">{seat.name}</span>
      </div>
    </div>
  )

  // 空状态显示
  const renderEmptyState = () => (
    <div className="flex h-full flex-col items-center justify-center text-gray-400">
      <div className="mb-4 h-32 w-32 opacity-30">
        {/* 可以替换为图标 */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
          />
        </svg>
      </div>
      <p className="text-lg">请输入1-9之间的席位数量</p>
    </div>
  )

  return (
    <div className="flex h-[850px] flex-col">
      <div className="flex h-[100px] w-full flex-shrink-0 items-center rounded-t-3xl bg-[#2657A4] px-4 py-2 text-white">
        <span className="ml-4 text-[26px] font-black">请输入席位数量</span>
        <Input
          placeholder="请输入1-9数字"
          className="!focus:bg-[#16325E] ml-4 h-9 w-36 rounded-md border-none bg-[#16325E] px-4 py-2 text-white hover:bg-[#16325E]"
          value={searchTerm}
          onChange={e => {
            // 过滤非1-9的字符
            const filteredValue = e.target.value.replace(/[^1-9]/g, "")
            setSearchTerm(filteredValue)
          }}
          maxLength={1}
          pattern="[1-9]+"
        />
        <span className="ml-4 text-[26px]">当前已选{}个角色</span>
      </div>
      <div className="flex w-full flex-1 flex-col items-center rounded-b-3xl bg-[#132B51] p-12 pb-9">
        {isEmpty ? (
          renderEmptyState()
        ) : (
          <div className="grid w-full grid-cols-3 gap-6">
            {seats.map(seat => renderSeatCard(seat))}
          </div>
        )}
      </div>
    </div>
  )
}

export default Characters
