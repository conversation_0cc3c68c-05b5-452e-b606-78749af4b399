import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/request';
import { Character } from '../types';

const CreateCharacter: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    avatar: '',
    description: '',
    attributes: {
      strength: 10,
      agility: 10,
      intelligence: 10,
      charisma: 10,
    },
    skills: [] as string[],
    background: '',
  });
  const [skillInput, setSkillInput] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await api.post<Character>('/characters', formData);
      navigate('/characters');
    } catch (error) {
      console.error('创建卡牌失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const addSkill = () => {
    if (skillInput.trim() && !formData.skills.includes(skillInput.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, skillInput.trim()]
      }));
      setSkillInput('');
    }
  };

  const removeSkill = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(s => s !== skill)
    }));
  };

  return (
    <div className="max-w-2xl mx-auto">
      <h1 className="text-3xl font-bold text-white mb-8">新增人物卡牌</h1>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-white mb-2">角色名称</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full p-3 bg-primary-dark text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none"
            required
          />
        </div>

        <div>
          <label className="block text-white mb-2">头像URL</label>
          <input
            type="url"
            value={formData.avatar}
            onChange={(e) => setFormData(prev => ({ ...prev, avatar: e.target.value }))}
            className="w-full p-3 bg-primary-dark text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-white mb-2">角色描述</label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full p-3 bg-primary-dark text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none h-24"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          {Object.entries(formData.attributes).map(([key, value]) => (
            <div key={key}>
              <label className="block text-white mb-2 capitalize">
                {key === 'strength' ? '力量' : 
                 key === 'agility' ? '敏捷' :
                 key === 'intelligence' ? '智力' : '魅力'}
              </label>
              <input
                type="number"
                min="1"
                max="20"
                value={value}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  attributes: {
                    ...prev.attributes,
                    [key]: parseInt(e.target.value)
                  }
                }))}
                className="w-full p-3 bg-primary-dark text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none"
              />
            </div>
          ))}
        </div>

        <div>
          <label className="block text-white mb-2">技能</label>
          <div className="flex gap-2 mb-2">
            <input
              type="text"
              value={skillInput}
              onChange={(e) => setSkillInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
              className="flex-1 p-3 bg-primary-dark text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none"
              placeholder="输入技能名称"
            />
            <button
              type="button"
              onClick={addSkill}
              className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              添加
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {formData.skills.map((skill) => (
              <span
                key={skill}
                className="bg-primary-light text-white px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-red-600 transition-colors"
                onClick={() => removeSkill(skill)}
              >
                {skill} ×
              </span>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-white mb-2">背景故事</label>
          <textarea
            value={formData.background}
            onChange={(e) => setFormData(prev => ({ ...prev, background: e.target.value }))}
            className="w-full p-3 bg-primary-dark text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none h-32"
          />
        </div>

        <button
          type="submit"
          disabled={loading}
          className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          {loading ? '创建中...' : '创建卡牌'}
        </button>
      </form>
    </div>
  );
};

export default CreateCharacter;