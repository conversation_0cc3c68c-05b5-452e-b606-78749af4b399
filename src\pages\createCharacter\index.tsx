import React, { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Character } from "@/types"
import { create<PERSON><PERSON>cter } from "@/api/createCharacter"
import RadarCharts from "./compoents/RadarChart"
import BarCharts from "./compoents/Barchart"
import { BarChart } from "lucide-react"
import { Button, Form, FormProps, Input } from "antd"
import { roleField } from "./utils"

const CreateCharacter: React.FC = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    name: "",
    avatar: "",
    description: "",
    attributes: {
      strength: 10,
      agility: 10,
      intelligence: 10,
      charisma: 10,
    },
    skills: [] as string[],
    background: "",
  })
  const [skillInput, setSkillInput] = useState("")
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // await create<PERSON><PERSON>cter(formData);
      navigate("/characters")
    } catch (error) {
      console.error("创建卡牌失败:", error)
    } finally {
      setLoading(false)
    }
  }

  const addSkill = () => {
    if (skillInput.trim() && !formData.skills.includes(skillInput.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, skillInput.trim()],
      }))
      setSkillInput("")
    }
  }

  const removeSkill = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(s => s !== skill),
    }))
  }
  const onFinish: FormProps<roleField>["onFinish"] = values => {
    console.log("Success:", values)
  }

  const onFinishFailed: FormProps<roleField>["onFinishFailed"] = errorInfo => {
    console.log("Failed:", errorInfo)
  }

  return (
    <div className="h-full">
      <div className="mx-auto flex h-[420px] w-full flex-col">
        <div className="flex h-[100px] w-full items-center rounded-t-xl bg-[#2657A4] px-4 py-2 text-white">
          <span className="ml-4 text-[32px] font-black">手动输入人物描述</span>
        </div>
        <div className="flex flex-1 rounded-b-xl bg-[#132B51]">
          <Form
            name="basic"
            labelCol={{ span: 2 }}
            wrapperCol={{ span: 20 }}
            initialValues={{ remember: true }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            style={{ width: "100%" }}
            className="mt-6"
            requiredMark={false}
          >
            <Form.Item<roleField>
              label="姓名"
              name="rolename"
              className="ml-6 text-white"
              labelAlign="left"
              rules={[{ required: true, message: "请输入姓名!" }]}
            >
              <Input
                placeholder="请输入姓名"
                className="!h-10 w-full resize-none border-[#295DB0] bg-[#09273E] text-white"
              />
            </Form.Item>
            <Form.Item<roleField>
              label="人物描述"
              name="description"
              className="ml-6 text-white"
              labelAlign="left"
              rules={[{ required: true, message: "请输入人物描述!" }]}
            >
              <Input.TextArea
                value={formData.background}
                placeholder="请输入对一个人物的描述"
                onChange={e => setFormData(prev => ({ ...prev, background: e.target.value }))}
                className="!h-36 w-full resize-none border-[#295DB0] bg-[#09273E] p-3 pb-0 text-white"
                style={{ scrollbarWidth: "none" }}
              />
            </Form.Item>
            <Form.Item label={null} className="flex items-center justify-center">
              <Button
                htmlType="submit"
                disabled={loading}
                loading={loading}
                className="z-10 flex h-10 w-48 items-center justify-center rounded-xl bg-[#2A81FF] text-white transition-colors hover:bg-[#2f86ff] disabled:opacity-50"
              >
                {loading ? "分析中..." : "分析人物性格"}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
      <div className="mx-auto mt-5 flex h-[500px] w-full flex-col">
        <div className="flex h-[100px] w-full items-center rounded-t-xl bg-[#2657A4] px-4 py-2 text-white">
          <span className="ml-4 text-[32px] font-black">五因素人格维度</span>
        </div>
        <div className="flex flex-1 items-center justify-center bg-[#132B51]">
          <RadarCharts
            data={[
              { name: "开放性", value: 100 },
              { name: "神经质", value: 10 },
              { name: "宜人性", value: 75 },
              { name: "外向性", value: 80 },
              { name: "尽责性", value: 88 },
            ]}
            width={500}
            height="320px"
            margin="0"
            center={["40%", "55%"]}
          />
          <BarCharts
            data={[
              { name: "开放性", value: 100 },
              { name: "神经质", value: 10 },
              { name: "宜人性", value: 75 },
              { name: "外向性", value: 80 },
              { name: "尽责性", value: 88 },
            ]}
          ></BarCharts>
        </div>
        <div className="flex items-center justify-center rounded-b-xl bg-[#132B51] pb-3">
          <Button
            className="z-10 flex h-10 w-48 items-center justify-center rounded-xl bg-[#2A81FF] text-white transition-colors hover:bg-[#2f86ff] disabled:opacity-50"
            onClick={() => navigate("/")}
          >
            保存并确认
          </Button>
        </div>
      </div>
    </div>
  )
}

export default CreateCharacter
