.processTable.ant-table-wrapper .ant-table-thead > tr > th {
  background: #2657a4;
  color: #ffffff;
  font-size: 26px;
  font-weight: 900;
  height: 100px;
  border-bottom: none;
}

.processTable.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not(
    [colspan]
  )::before {
  background-color: transparent !important;
}
.processTable.ant-table-wrapper .ant-table-tbody > tr > td {
  background: #132b51;
  border-bottom: 1px solid #ffffff33;
  font-size: 24px;
  font-weight: 400;
  color: #ffffff;
  height: 100px !important;
}
.processTable.ant-table-wrapper .ant-table-tbody .ant-table-row > .ant-table-cell-row-hover {
  background: #132b51;
}
.process-page.ant-pagination .ant-pagination-disabled .ant-pagination-item-link {
  color: #ffffff !important;
}
.process-page.ant-pagination .ant-pagination-prev .ant-pagination-item-link,
.process-page.ant-pagination .ant-pagination-next .ant-pagination-item-link,
.process-page.ant-pagination.ant-pagination .ant-pagination-jump-next,
.process-page.ant-pagination .ant-pagination-item-active,
.process-page.ant-pagination .ant-pagination-item,
.process-page.ant-pagination
  .ant-pagination-jump-prev
  .ant-pagination-item-container
  .ant-pagination-item-ellipsis,
.process-page.ant-pagination
  .ant-pagination-jump-next
  .ant-pagination-item-container
  .ant-pagination-item-ellipsis,
.process-page.ant-pagination
  .ant-pagination-jump-next
  .ant-pagination-item-container
  .ant-pagination-item-ellipsis {
  background-color: #132b51;
  color: #ffffff !important;
  border-color: transparent !important;
}
.process-page.ant-pagination .ant-pagination-item-active {
  border-color: #5189fa !important;
}
.process-page.ant-pagination .ant-pagination-item > a {
  color: #ffffff !important;
}
.process-page.ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover {
  background-color: #132b51 !important;
}
.process-page.ant-pagination-jump-next
  .ant-pagination-item-container
  .ant-pagination-item-ellipsis {
  color: #ffffff !important;
}
.process-page.ant-pagination .ant-pagination-options-quick-jumper {
  color: #ffffff !important;
}
.process-page.ant-pagination .ant-pagination-options-quick-jumper input {
  background: #132b51 !important;
  border-color: transparent !important;
  color: #ffffff !important;
}
.process-page
  .ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background: #3d66a7 !important;
}
.process-page .ant-select-dropdown {
  background: #132b51 !important;
}
.process-page .ant-select-item-option-content {
  color: #ffffff !important;
}
.ant-select-outlined .ant-select-selector {
  background: #132b51 !important;
  border-color: transparent !important;
  color: #ffffff !important;
}
.ant-select .ant-select-arrow {
  color: #ffffff !important;
}
.processTable .ant-spin-nested-loading > div > .ant-spin {
  max-height: 800px;
}
.processTable.ant-table-wrapper table {
  /* min-height: 800px; */
}
