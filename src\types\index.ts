// 人物卡牌类型
export interface Character {
  id: string;
  name: string;
  avatar: string;
  description: string;
  attributes: {
    strength: number;
    agility: number;
    intelligence: number;
    charisma: number;
  };
  skills: string[];
  background: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

// 讨论类型
export interface Discussion {
  id: string;
  characterId: string;
  title: string;
  content: string;
  author: string;
  replies: Reply[];
  createdAt: string;
  updatedAt: string;
}

export interface Reply {
  id: string;
  content: string;
  author: string;
  createdAt: string;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}