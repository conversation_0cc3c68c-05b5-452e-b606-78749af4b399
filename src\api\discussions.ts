import { httpClient } from '@/utils/request';
import { Discussion } from '@/types';

export const getDiscussions = async (): Promise<Discussion[]> => {
    return httpClient.get<Discussion[]>('/discussions');
};

export const createDiscussion = async (discussion: Discussion): Promise<Discussion> => {
    return httpClient.post<Discussion>('/discussions', discussion);
};

export const getDiscussion = async (id: string): Promise<Discussion> => {
    return httpClient.get<Discussion>(`/discussions/${id}`);
};

export const updateDiscussion = async (discussion: Discussion): Promise<Discussion> => {
    return httpClient.put<Discussion>(`/discussions/${discussion.id}`, discussion);
};

export const deleteDiscussion = async (id: string): Promise<void> => {
    return httpClient.delete<void>(`/discussions/${id}`);
};
