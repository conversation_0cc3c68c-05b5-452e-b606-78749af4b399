import React from 'react';
import { Link } from 'react-router-dom';
import { Plus, Users, MessageSquare } from 'lucide-react';

const Home: React.FC = () => {
  return (
    <div className="text-center">
      <h1 className="text-4xl font-bold text-white mb-8">
        欢迎来到人物卡牌系统
      </h1>
      
      <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
        创建、管理和讨论你的角色扮演游戏人物卡牌
      </p>

      <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        <Link
          to="/create"
          className="bg-primary-dark hover:bg-primary-light transition-colors p-8 rounded-lg text-center group"
        >
          <Plus className="w-12 h-12 text-blue-400 mx-auto mb-4 group-hover:scale-110 transition-transform" />
          <h3 className="text-xl font-semibold text-white mb-2">新增卡牌</h3>
          <p className="text-gray-400">创建新的人物卡牌</p>
        </Link>

        <Link
          to="/characters"
          className="bg-primary-dark hover:bg-primary-light transition-colors p-8 rounded-lg text-center group"
        >
          <Users className="w-12 h-12 text-green-400 mx-auto mb-4 group-hover:scale-110 transition-transform" />
          <h3 className="text-xl font-semibold text-white mb-2">卡牌选择</h3>
          <p className="text-gray-400">浏览所有人物卡牌</p>
        </Link>

        <Link
          to="/discussions"
          className="bg-primary-dark hover:bg-primary-light transition-colors p-8 rounded-lg text-center group"
        >
          <MessageSquare className="w-12 h-12 text-purple-400 mx-auto mb-4 group-hover:scale-110 transition-transform" />
          <h3 className="text-xl font-semibold text-white mb-2">卡牌讨论</h3>
          <p className="text-gray-400">参与社区讨论</p>
        </Link>
      </div>
    </div>
  );
};

export default Home;