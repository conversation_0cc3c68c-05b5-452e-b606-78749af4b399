import Cookies from 'js-cookie';

const TOKEN_KEY = 'auth_token';

export const cookieUtils = {
  // 设置token
  setToken: (token: string, expires: number = 7) => {
    Cookies.set(TOKEN_KEY, token, { expires, secure: true, sameSite: 'strict' });
  },

  // 获取token
  getToken: (): string | undefined => {
    return Cookies.get(TOKEN_KEY);
  },

  // 删除token
  removeToken: () => {
    Cookies.remove(TOKEN_KEY);
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    return !!Cookies.get(TOKEN_KEY);
  }
};