{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^24.0.7", "antd": "^5.26.3", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "postcss": "^8.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.3", "tailwindcss": "3.4.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.19.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0", "husky": "^9.1.4", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "latest", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}, "lint-staged": {"*.js": "eslint --fix", "*.ts": "eslint --fix", "*.jsx": "eslint --fix", "*.tsx": "eslint --fix", "*.json": "prettier --write", "*.css": "prettier --write"}}