import React from "react"
import { Progress, Tooltip } from "antd"

interface BarDataItem {
  name: string
  value: number
}

interface BarChartProps {
  data?: BarDataItem[]
}

const BarCharts: React.FC<BarChartProps> = React.memo(({ data = [] }) => {
  const colors = ["#0075FF", "#FFCF43", "#FF7D7D", "#7489FF", "#00DE75"]
  const sortedData = [...data].sort((a, b) => b.value - a.value)

  return (
    <div className="flex w-[400px] flex-col justify-center pr-28">
      {sortedData.map((item, index) => (
        <div className="mb-3" key={item.name}>
          <div className="text-white">{item.name}</div>
          <Tooltip title={`${item.value}`} placement="right">
            <Progress
              percent={item.value}
              trailColor="rgba(255, 255, 255, 0.1)"
              showInfo={false}
              strokeColor={colors[index % colors.length]}
            />
          </Tooltip>
        </div>
      ))}
    </div>
  )
})

export default BarCharts
