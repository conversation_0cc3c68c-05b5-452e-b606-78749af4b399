import React, { useRef } from 'react';
import CardICon from '@/assets/card-icon.svg';
import { useNavigate } from 'react-router-dom';

const Home: React.FC = () => {
  const cardRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const handleMouseMove = (e: { clientX: number; clientY: number; }) => {
    const card = cardRef.current;
    if (!card) return;

    const { left, top, width, height } = card.getBoundingClientRect();
    const x = (e.clientX - left) / width;
    const y = (e.clientY - top) / height;

    // 3D倾斜效果
    const tiltX = (y - 0.5) * 20;
    const tiltY = (0.5 - x) * 20;

    // 光影渐变位置
    const gradientPos = `${x * 100}% ${y * 100}%`;

    // 动态阴影位置
    const shadowX = (x - 0.5) * 20;
    const shadowY = (y - 0.5) * 20;

    card.style.setProperty('--tilt-x', `${tiltX}deg`);
    card.style.setProperty('--tilt-y', `${tiltY}deg`);
    card.style.setProperty('--gradient-pos', gradientPos);
    card.style.setProperty('--shadow-x', `${shadowX}px`);
    card.style.setProperty('--shadow-y', `${shadowY}px`);
  };

  const handleMouseLeave = () => {
    const card = cardRef.current;
    if (!card) return;

    card.style.setProperty('--tilt-x', '0deg');
    card.style.setProperty('--tilt-y', '0deg');
    card.style.setProperty('--gradient-pos', '50% 50%');
    card.style.setProperty('--shadow-x', '0px');
    card.style.setProperty('--shadow-y', '0px');
  };
  return (
    <div className="text-center flex flex-col content-center items-center mt-28">
      <h1 className="text-[clamp(2rem,5vw,5rem)] font-bold text-white mb-9 leading-[160%]">
        <span>创建专属</span>
        <span className='text-transparent bg-custom-gradient bg-clip-text'>人物卡牌</span>
      </h1>
      <span className='text-2xl'>通过人物卡牌选择系统，定制所想的角色卡牌，完成所需的团队讨论与总结</span>
      <div
        ref={cardRef}
        className="bg-[#263447] border-1 border-[#144597]
      rounded-3xl shadow-md py-8 px-3 card-hover h-[32rem] w-[50rem] mt-14
      hover:-translate-y-1 transition-all relative overflow-hidden
      before:content-[''] before:absolute before:inset-0 before:bg-[radial-gradient(circle_at_var(--gradient-pos),_rgba(42,129,255,0.2)_0%,_transparent_70%)] before:opacity-0 before:transition-opacity before:duration-300
      hover:before:opacity-100
      "
        style={{
          transform: 'perspective(1000px) rotateX(var(--tilt-x)) rotateY(var(--tilt-y))',
          boxShadow: '0 10px 30px -5px rgba(0, 0, 0, 0.3), var(--shadow-x) var(--shadow-y) 30px -5px rgba(42,129,255,0.4)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        }}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        <div className="flex justify-end items-center">
          <div className="space-x-2 flex justify-center rounded-xl items-center bg-[#2A81FF] w-36 h-10 cursor-pointer z-10 hover:bg-[#1a6fe0] transition-colors"
            onClick={() => navigate('/create')}
          >
            <span>新增人物卡牌</span>
          </div>
        </div>
        <div className='px-14 pt-4 flex flex-col items-start justify-start'>
          <img src={CardICon} alt="" className='mb-16 w-10 h-10' />
          <span className='text-[2rem] font-bold text-white'>人物卡片选择</span>
          <span className='text-2xl text-left mt-10'>从人物文档数据库中<br /> 选择已有的人物卡牌来进行设置</span>
          <div className="space-x-2 flex justify-center rounded-xl items-center border z-10 border-[#2A81FF] w-full h-20 cursor-pointer mt-10 hover:bg-[#2A81FF] hover:text-white transition-all group"
            onClick={() => navigate('/characters')}
          >
            <span className='text-[#2A81FF] font-black text-[2rem] group-hover:text-white transition-colors '>卡牌选择</span>
          </div>
        </div>

        {/* 添加一些闪烁的装饰元素 */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none overflow-hidden">
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-white rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-1000"
              style={{
                width: `${Math.random() * 100 + 50}px`,
                height: `${Math.random() * 100 + 50}px`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                filter: 'blur(20px)',
                animation: `pulse ${Math.random() * 3 + 2}s infinite alternate`,
              }}
            />
          ))}
        </div>
      </div>

    </div>
  );
};

export default Home;