import React from "react"
import { Link, useNavigate } from "react-router-dom"
import { User, LogOut } from "lucide-react"
import { cookieUtils } from "@/utils/cookie"
import goBackIcon from "@/assets/goback-icon.svg"
import logo from "@/assets/logo.svg"

interface HeaderProps {
  className?: string
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const navigate = useNavigate()
  const isAuthenticated = cookieUtils.isAuthenticated()

  const handleLogout = () => {
    cookieUtils.removeToken()
    navigate("/login")
  }
  const handleGoBack = () => {
    if (window.location.pathname === "/") {
      // navigate("/home");
    } else {
      navigate(-1)
    }
  }
  const headerText = () => {
    if (window.location.pathname === "/") {
      return ""
    } else if (window.location.pathname === "/create") {
      return "-人物卡片创建"
    } else if (window.location.pathname === "/characters") {
      return "-卡牌选择"
    } else if (window.location.pathname === "/discussions") {
      return "-卡牌讨论"
    } else if (window.location.pathname === "/deductionProcess") {
      return "-智能蓝方出牌"
    } else {
      return ""
    }
  }
  return (
    <header className={`bg-primary-header shadow-lg ${className}`}>
      <div className="container px-5">
        <div className="flex h-24 items-center justify-between">
          <div className="flex items-center space-x-4">
            <img
              src={goBackIcon}
              onClick={handleGoBack}
              alt="Go Back"
              className="mr-6 h-14 w-14 cursor-pointer space-x-2"
            />
            <Link to="/" className="text-xl font-bold text-white">
              {/* <img src={logo} alt="Logo" className="w-20 h-20 mr-9 inline-block" /> */}
              智子系统{headerText()}
            </Link>
          </div>

          {/* <nav className="hidden md:flex space-x-6">
            <Link to="/" className="text-white hover:text-blue-200 transition-colors">
              首页
            </Link>
            <Link to="/create" className="text-white hover:text-blue-200 transition-colors">
              新增卡牌
            </Link>
            <Link to="/characters" className="text-white hover:text-blue-200 transition-colors">
              卡牌选择
            </Link>
            <Link to="/discussions" className="text-white hover:text-blue-200 transition-colors">
              卡牌讨论
            </Link>
          </nav> */}

          {/* <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                <User className="w-6 h-6 text-white" />
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-1 text-white hover:text-blue-200 transition-colors"
                >
                  <LogOut className="w-4 h-4" />
                  <span>退出</span>
                </button>
              </>
            ) : (
              <Link to="/login" className="text-white hover:text-blue-200 transition-colors">
                登录
              </Link>
            )}
          </div> */}
        </div>
      </div>
    </header>
  )
}

export default Header
