import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { User, LogOut } from 'lucide-react';
import { cookieUtils } from '../../utils/cookie';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const isAuthenticated = cookieUtils.isAuthenticated();

  const handleLogout = () => {
    cookieUtils.removeToken();
    navigate('/login');
  };

  return (
    <header className="bg-primary-header shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="text-xl font-bold text-white">
            人物卡牌系统
          </Link>
          
          <nav className="hidden md:flex space-x-6">
            <Link to="/" className="text-white hover:text-blue-200 transition-colors">
              首页
            </Link>
            <Link to="/create" className="text-white hover:text-blue-200 transition-colors">
              新增卡牌
            </Link>
            <Link to="/characters" className="text-white hover:text-blue-200 transition-colors">
              卡牌选择
            </Link>
            <Link to="/discussions" className="text-white hover:text-blue-200 transition-colors">
              卡牌讨论
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                <User className="w-6 h-6 text-white" />
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-1 text-white hover:text-blue-200 transition-colors"
                >
                  <LogOut className="w-4 h-4" />
                  <span>退出</span>
                </button>
              </>
            ) : (
              <Link to="/login" className="text-white hover:text-blue-200 transition-colors">
                登录
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;