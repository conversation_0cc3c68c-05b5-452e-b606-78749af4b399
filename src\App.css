/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  /* 滚动条宽度 */
  height: 8px;
  /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* 滚动条轨道背景 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  /* 滚动条滑块颜色 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* 滑块悬停时的颜色 */
}
.ant-input-outlined:hover {
  background-color: #16325e !important;
}
.ant-input-outlined:focus {
  border-color: #295db0 !important;
  background-color: #16325e;
}
.ant-form-item .ant-form-item-label > label {
  color: white !important;
}
.ant-input-outlined.ant-input-status-error {
  background-color: transparent !important;
}

.ant-input::placeholder {
  color: #898989;
}
.ant-btn-variant-outlined {
  border-color: #295db0;
}
button:focus {
  outline: none;
}
