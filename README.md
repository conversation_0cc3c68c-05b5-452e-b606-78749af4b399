## 开发规范 
  1. 函数和 jsx 分开写，不要内嵌函数实现
  2. 一个组件不要写太多行，一般150 -200 行左右。过多的话一般来讲可以拆分成小组件问。
  3. useEffect 和 useState 相关比较强的写成一个hook，方便复用。
  4. 尽量不要出现重复代码。
  5. 尽量不要硬编码，常量使用const 定义。
  6. Util 工具函数或者类需要写完善类型。保持纯函数
  7. Ts 组件prop ，前后端的数据结构，类型定义，ts + 组件any ，减少any，尽量写完善

## 拓展eslint 配置


- 配置 `parserOptions` ：

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

- 把 `tseslint.configs.recommended` 改成 `tseslint.configs.recommendedTypeChecked` 或者 `tseslint.configs.strictTypeChecked`
- 可选添加 `...tseslint.configs.stylisticTypeChecked`
- 安装 [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) 更新配置:

```js
// eslint.config.js
import react from 'eslint-plugin-react'

export default tseslint.config({
  // Set the react version
  settings: { react: { version: '18.3' } },
  plugins: {
    // Add the react plugin
    react,
  },
  rules: {
    // other rules...
    // Enable its recommended rules
    ...react.configs.recommended.rules,
    ...react.configs['jsx-runtime'].rules,
  },
})
```
