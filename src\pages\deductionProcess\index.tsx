import { Button, Pagination, Progress, Table, TablePaginationConfig } from "antd"
import "./index.css"
import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"

const DeductionProcessList = () => {
  interface DataType {
    id: string
    key: string
    taskName: string
    startTime: string
    progress: number
    status: string
    operator: string
  }

  interface TableParams {
    pagination?: TablePaginationConfig
  }
  const navigate = useNavigate()

  const [loading, setLoading] = useState(false)
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 7, // 每页最多7条数据
      pageSizeOptions: ["7", "14", "21", "28"], // 调整每页数量选项
    },
  })
  const [dataSource, setDataSource] = useState<DataType[]>([])
  const [total, setTotal] = useState(0)

  // 模拟生成100条任务数据
  const generateMockData = (count: number): DataType[] => {
    const tasks = []
    const statusOptions = ["待处理", "处理中", "已完成"]
    const operators = ["张三", "李四", "王五", "赵六", "钱七"]

    for (let i = 1; i <= count; i++) {
      const status = statusOptions[Math.floor(Math.random() * 3)]
      let progress = 0

      switch (status) {
        case "待处理":
          progress = 0
          break
        case "处理中":
          progress = Math.floor(Math.random() * 90) + 1
          break
        case "已完成":
          progress = 100
          break
        default:
          progress = 0
      }

      tasks.push({
        id: i.toString(),
        key: i.toString(),
        taskName: `任务-${i.toString().padStart(3, "0")}`,
        startTime: new Date(
          Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)
        ).toLocaleString(),
        progress,
        status,
        operator: operators[Math.floor(Math.random() * operators.length)],
      })
    }

    return tasks
  }

  // 处理分页、排序、筛选变化
  const handleTableChange = (pagination: TablePaginationConfig) => {
    setTableParams({
      pagination,
    })
  }

  // 模拟数据加载
  useEffect(() => {
    setLoading(true)

    // 模拟API请求延迟
    setTimeout(() => {
      const mockData = generateMockData(100) // 生成100条数据
      setTotal(mockData.length)

      // 根据当前分页参数截取数据
      const { current = 1, pageSize = 7 } = tableParams.pagination || {}
      const startIndex = (current - 1) * pageSize
      const endIndex = startIndex + pageSize

      setDataSource(mockData.slice(startIndex, endIndex))
      setLoading(false)
    }, 800)
  }, [tableParams.pagination])

  const columns = [
    {
      title: "任务名称",
      dataIndex: "taskName",
      key: "taskName",
      width: 200,
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      key: "startTime",
      width: 180,
    },
    // {
    //   title: "状态",
    //   dataIndex: "status",
    //   key: "status",
    //   width: 100,
    //   render: text => {
    //     let color = "#bfbfbf"
    //     if (text === "处理中") color = "#1890ff"
    //     if (text === "已完成") color = "#52c41a"

    //     return <span style={{ color }}>{text}</span>
    //   },
    // },
    {
      title: "进度",
      dataIndex: "progress",
      key: "progress",
      width: 220,
      render: (percent: number) => (
        <div className="flex items-center">
          <span className="mr-3">{percent}%</span>
          <Progress
            percent={percent}
            size={[200, 12]}
            showInfo={false}
            trailColor="rgba(255, 255, 255, 0.1)"
            status={percent === 0 ? "normal" : percent === 100 ? "success" : "active"}
          />
        </div>
      ),
    },
    // {
    //   title: "操作员",
    //   dataIndex: "operator",
    //   key: "operator",
    //   width: 100,
    // },
    {
      title: "操作",
      key: "action",
      width: 100,
      render: (_, record: DataType) => (
        <Button
          type="primary"
          className="w-24 rounded-xl bg-[#2A81FF]"
          onClick={() => {
            console.log("进入任务详情", record)
            navigate(`/characters/${record.id}`)
          }}
          //   disabled={record.status === "已完成" || record.status === "待处理"}
        >
          进入
        </Button>
      ),
    },
  ]

  return (
    <div>
      <Table
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        className="processTable min-h-[600px]"
        pagination={false}
      />
      <div className="mt-8 flex items-center justify-between">
        <div>共 {total} 条记录</div>
        <Pagination
          className="process-page"
          align="center"
          current={tableParams.pagination?.current || 1}
          pageSize={tableParams.pagination?.pageSize || 7}
          total={total}
          showSizeChanger
          showQuickJumper
          pageSizeOptions={["7", "14", "21", "28"]} // 每页数量选项
          onChange={(page, pageSize) => {
            handleTableChange({
              ...tableParams.pagination,
              current: page,
              pageSize,
            })
          }}
          onShowSizeChange={(current, size) => {
            handleTableChange({
              ...tableParams.pagination,
              current,
              pageSize: size,
            })
          }}
          locale={{
            items_per_page: "条/页",
            jump_to: "跳转至",
            jump_to_confirm: "确定",
            page: "页",
          }}
        />
      </div>
    </div>
  )
}

export default DeductionProcessList
