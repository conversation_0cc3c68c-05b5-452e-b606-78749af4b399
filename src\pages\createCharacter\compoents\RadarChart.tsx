import React, { useEffect, useRef, useMemo } from "react"
import * as echarts from "echarts/core"
import {
  TitleComponent,
  TitleComponentOption,
  LegendComponent,
  LegendComponentOption,
  TooltipComponent,
  TooltipComponentOption,
} from "echarts/components"
import { RadarChart, RadarSeriesOption } from "echarts/charts"
import { CanvasRenderer } from "echarts/renderers"

echarts.use([TitleComponent, LegendComponent, TooltipComponent, RadarChart, CanvasRenderer])

type EChartsOption = echarts.ComposeOption<
  TitleComponentOption | LegendComponentOption | RadarSeriesOption | TooltipComponentOption
>

interface RadarDataItem {
  name: string
  value: number
}

interface RadarChartProps {
  data?: RadarDataItem[]
  width?: string | number // 新增：宽度属性
  height?: string | number // 新增：高度属性
  margin?: string // 新增：边距属性
  center?: [string, string] // 新增：图表中心位置
}

const RadarCharts: React.FC<RadarChartProps> = React.memo(
  ({
    data = [],
    width = "100%", // 默认宽度为父容器的100%
    height = "400px", // 默认高度
    margin = "0 auto", // 默认居中
    center = ["50%", "50%"], // 默认居中
  }) => {
    const chartRef = useRef<HTMLDivElement>(null)
    const chartInstance = useRef<echarts.ECharts | null>(null)

    const chartOption = useMemo<EChartsOption>(() => {
      // 处理传入的数据
      const maxValue = Math.max(...data.map(item => item.value), 100) // 最小值为100
      const indicators = data.map(item => ({
        name: item.name,
        max: maxValue, // 最大值增加20%的填充
      }))

      const values = data.map(item => item.value)

      return {
        tooltip: {
          trigger: "item",
          backgroundColor: "#1a2332",
          textStyle: {
            color: "#fff",
          },
        },
        legend: {
          // data: ['Your Score'],
          textStyle: {
            color: "#fff",
          },
        },
        radar: {
          indicator: indicators,
          center: center, // 设置雷达图在容器中的中心位置
          axisName: {
            color: "#fff",
            fontSize: 24,
            fontWeight: 500,
          },
          splitLine: {
            lineStyle: {
              color: "#fff",
              type: "dashed", // 网格线为虚线
              opacity: 0.5,
            },
          },
          splitArea: {
            show: false, // 隐藏分割区域
          },
          axisLine: {
            lineStyle: {
              color: "#fff",
              type: "dashed", // 坐标轴为虚线
              opacity: 0.5,
            },
          },
          splitNumber: 4, // 同心圆数量
        },
        series: [
          {
            name: "Score",
            type: "radar",
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#fff",
            },
            lineStyle: {
              width: 2,
            },
            areaStyle: {
              color: "rgba(42, 129, 255, 0.4)",
            },
            data: [
              {
                value: values,
                // name: 'Your Score'
              },
            ],
          },
        ],
      }
    }, [data, center])

    useEffect(() => {
      if (!chartRef.current) return

      chartInstance.current = echarts.init(chartRef.current)
      chartInstance.current.setOption(chartOption)

      const handleResize = () => {
        chartInstance.current?.resize()
      }

      window.addEventListener("resize", handleResize)

      return () => {
        window.removeEventListener("resize", handleResize)
        chartInstance.current?.dispose()
      }
    }, [chartOption])

    // 处理宽度和高度的样式值
    const getDimensionStyle = (dimension: string | number) => {
      return typeof dimension === "number" ? `${dimension}px` : dimension
    }

    return (
      <div
        ref={chartRef}
        style={{
          width: getDimensionStyle(width),
          height: getDimensionStyle(height),
          margin: margin,
          position: "relative", // 确保定位正常工作
        }}
      />
    )
  }
)

export default RadarCharts
