import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { MessageSquare, User, Clock } from 'lucide-react';
import { api } from '../utils/request';
import { Discussion } from '../types';

const Discussions: React.FC = () => {
  const [discussions, setDiscussions] = useState<Discussion[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDiscussions();
  }, []);

  const fetchDiscussions = async () => {
    try {
      const data = await api.get<Discussion[]>('/discussions');
      setDiscussions(data);
    } catch (error) {
      console.error('获取讨论失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  if (loading) {
    return (
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
        <p className="mt-4">加载中...</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-white">卡牌讨论</h1>
        <Link
          to="/discussions/new"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          发起讨论
        </Link>
      </div>

      <div className="space-y-4">
        {discussions.map((discussion) => (
          <div key={discussion.id} className="bg-primary-dark rounded-lg p-6 hover:bg-primary-light transition-colors">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Link
                  to={`/discussions/${discussion.id}`}
                  className="text-xl font-semibold text-white hover:text-blue-400 transition-colors"
                >
                  {discussion.title}
                </Link>
                <p className="text-gray-300 mt-2 line-clamp-2">
                  {discussion.content}
                </p>
                
                <div className="flex items-center space-x-4 mt-4 text-sm text-gray-400">
                  <div className="flex items-center space-x-1">
                    <User className="w-4 h-4" />
                    <span>{discussion.author}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{formatDate(discussion.createdAt)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="w-4 h-4" />
                    <span>{discussion.replies.length} 回复</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {discussions.length === 0 && (
        <div className="text-center text-gray-400 mt-12">
          <MessageSquare className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p>暂无讨论，快来发起第一个讨论吧！</p>
        </div>
      )}
    </div>
  );
};

export default Discussions;