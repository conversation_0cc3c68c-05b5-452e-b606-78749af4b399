import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { api } from '../utils/request';
import { Character } from '../types';

const Characters: React.FC = () => {
  const [characters, setCharacters] = useState<Character[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchCharacters();
  }, []);

  const fetchCharacters = async () => {
    try {
      const data = await api.get<Character[]>('/characters');
      setCharacters(data);
    } catch (error) {
      console.error('获取卡牌失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredCharacters = characters.filter(character =>
    character.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    character.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
        <p className="mt-4">加载中...</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-white">人物卡牌</h1>
        <Link
          to="/create"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          新增卡牌
        </Link>
      </div>

      <div className="mb-6">
        <input
          type="text"
          placeholder="搜索卡牌..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full max-w-md p-3 bg-primary-dark text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none"
        />
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCharacters.map((character) => (
          <div key={character.id} className="bg-primary-dark rounded-lg p-6 hover:bg-primary-light transition-colors">
            {character.avatar && (
              <img
                src={character.avatar}
                alt={character.name}
                className="w-20 h-20 rounded-full mx-auto mb-4 object-cover"
              />
            )}
            <h3 className="text-xl font-semibold text-white text-center mb-2">
              {character.name}
            </h3>
            <p className="text-gray-300 text-center mb-4 line-clamp-3">
              {character.description}
            </p>
            
            <div className="grid grid-cols-2 gap-2 mb-4 text-sm">
              <div className="text-gray-400">力量: <span className="text-white">{character.attributes.strength}</span></div>
              <div className="text-gray-400">敏捷: <span className="text-white">{character.attributes.agility}</span></div>
              <div className="text-gray-400">智力: <span className="text-white">{character.attributes.intelligence}</span></div>
              <div className="text-gray-400">魅力: <span className="text-white">{character.attributes.charisma}</span></div>
            </div>

            {character.skills.length > 0 && (
              <div className="mb-4">
                <div className="flex flex-wrap gap-1">
                  {character.skills.slice(0, 3).map((skill) => (
                    <span key={skill} className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                      {skill}
                    </span>
                  ))}
                  {character.skills.length > 3 && (
                    <span className="text-gray-400 text-xs">+{character.skills.length - 3}</span>
                  )}
                </div>
              </div>
            )}

            <Link
              to={`/characters/${character.id}`}
              className="block w-full text-center bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors"
            >
              查看详情
            </Link>
          </div>
        ))}
      </div>

      {filteredCharacters.length === 0 && (
        <div className="text-center text-gray-400 mt-12">
          <p>暂无卡牌数据</p>
        </div>
      )}
    </div>
  );
};

export default Characters;