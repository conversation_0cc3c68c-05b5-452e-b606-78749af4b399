import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout/Layout';
import Home from './pages/Home';
import CreateCharacter from './pages/CreateCharacter';
import Characters from './pages/Characters';
import Discussions from './pages/Discussions';
import './App.css';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/create" element={<CreateCharacter />} />
          <Route path="/characters" element={<Characters />} />
          <Route path="/discussions" element={<Discussions />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
