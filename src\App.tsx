import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import Layout from "@/components/Layout/Layout"
import Home from "@/pages/home/<USER>"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/pages/createCharacter"
import Characters from "@/pages/characters"
import Discussions from "@/pages/discussions"
import "@/App.css"
import DeductionProcess from "@/pages/deductionProcess"

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/create" element={<CreateCharacter />} />
          <Route path="/characters/:id" element={<Characters />} />
          <Route path="/discussions" element={<Discussions />} />
          <Route path="/deductionProcess" element={<DeductionProcess />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
